<template>
  <section class="app-banner">
    <div class="banner-container">
      <div class="banner-content">
        <h1 class="banner-title">Vue 3 + Element Plus</h1>
        <p class="banner-subtitle">现代化的开发者友好前端框架</p>
        <div class="banner-description">
          <p>基于 Vue 3、TypeScript 和 Element Plus 构建的企业级前端框架，提供丰富的组件和功能，助力快速开发高质量应用。</p>
        </div>
        <div class="banner-actions">
          <router-link to="/components" class="btn btn-primary">浏览组件</router-link>
          <router-link to="/docs" class="btn btn-secondary">阅读文档</router-link>
          <a href="https://github.com/your-repo/vue3-element-plus" target="_blank" class="btn btn-outline">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="btn-icon">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
            </svg>
            GitHub
          </a>
        </div>
        <div class="banner-stats">
          <div class="stat-item">
            <span class="stat-number">3.2.13</span>
            <span class="stat-label">Vue 版本</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">50+</span>
            <span class="stat-label">组件</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">100%</span>
            <span class="stat-label">TypeScript</span>
          </div>
        </div>
      </div>
      <div class="banner-decoration">
        <div class="code-block">
          <div class="code-header">
            <span class="code-title">main.ts</span>
            <div class="code-dots">
              <span class="dot dot-red"></span>
              <span class="dot dot-yellow"></span>
              <span class="dot dot-green"></span>
            </div>
          </div>
          <pre><code>
import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'

// 创建 Vue 应用
const app = createApp(App)

// 使用插件
app.use(ElementPlus)

// 挂载应用
app.mount('#app')
          </code></pre>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// Banner component for the homepage
</script>

<style lang="scss" scoped>
@import '@/styles/abstracts/_colors.scss';
@import '@/styles/abstracts/_mixins.scss';
@import '@/styles/base/_gradients.scss';

.app-banner {
  background: var(--gradient-blue-light);
  padding: 4rem 2rem;
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -10%;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba($color-brand-primary, 0.1) 0%, rgba($color-brand-primary, 0) 70%);
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -5%;
    left: -5%;
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba($color-brand-primary, 0.1) 0%, rgba($color-brand-primary, 0) 70%);
  }

  .banner-container {
    max-width: 1280px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
    gap: 3rem;

    @include respond-to(mobile) {
      flex-direction: column;
      gap: 2rem;
    }
  }

  .banner-content {
    flex: 1;
    max-width: 600px;
  }

  .banner-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-galaxy-blue);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1.2;

    @include respond-to(mobile) {
      font-size: 2rem;
    }
  }

  .banner-subtitle {
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    color: $color-text-secondary;
  }

  .banner-description {
    margin-bottom: 2rem;

    p {
      font-size: 1rem;
      line-height: 1.6;
      color: $color-text-secondary;
    }
  }

  .banner-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2.5rem;
    flex-wrap: wrap;

    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      font-weight: 500;
      font-size: 1rem;
      text-decoration: none;
      transition: all 0.2s ease;

      &-icon {
        margin-right: 0.5rem;
      }

      &-primary {
        background-color: $color-brand-primary;
        color: $color-text-white;
        box-shadow: 0 4px 6px rgba($color-brand-primary, 0.25);

        &:hover {
          background-color: $color-brand-hover;
          transform: translateY(-2px);
          box-shadow: 0 6px 10px rgba($color-brand-primary, 0.3);
        }

        &:active {
          background-color: $color-brand-active;
          transform: translateY(0);
        }
      }

      &-secondary {
        background-color: $color-fill-tag;
        color: $color-text-primary;

        &:hover {
          background-color: darken($color-fill-tag, 5%);
          transform: translateY(-2px);
        }

        &:active {
          background-color: darken($color-fill-tag, 10%);
          transform: translateY(0);
        }
      }

      &-outline {
        background-color: transparent;
        color: $color-text-secondary;
        border: 1px solid $color-border-base;
        display: inline-flex;
        align-items: center;

        svg {
          margin-right: 0.5rem;
        }

        &:hover {
          background-color: $color-fill-tag;
          color: $color-text-primary;
          transform: translateY(-2px);
        }

        &:active {
          background-color: darken($color-fill-tag, 5%);
          transform: translateY(0);
        }
      }
    }
  }

  .banner-stats {
    display: flex;
    gap: 2rem;

    @include respond-to(mobile) {
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .stat-number {
        font-size: 1.75rem;
        font-weight: 700;
        color: $color-brand-primary;
        margin-bottom: 0.25rem;
      }

      .stat-label {
        font-size: 0.875rem;
        color: $color-text-tertiary;
      }
    }
  }

  .banner-decoration {
    flex: 1;
    max-width: 500px;

    @include respond-to(mobile) {
      width: 100%;
      max-width: 100%;
    }

    .code-block {
      background-color: #1e1e1e;
      border-radius: 0.5rem;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      overflow: hidden;
      transform: perspective(800px) rotateY(-5deg);
      transition: transform 0.3s ease;

      &:hover {
        transform: perspective(800px) rotateY(0deg);
      }

      .code-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #2d2d2d;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #3a3a3a;

        .code-title {
          color: #e0e0e0;
          font-size: 0.875rem;
          font-family: monospace;
        }

        .code-dots {
          display: flex;
          gap: 0.5rem;

          .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;

            &-red {
              background-color: #ff5f56;
            }

            &-yellow {
              background-color: #ffbd2e;
            }

            &-green {
              background-color: #27c93f;
            }
          }
        }
      }

      pre {
        margin: 0;
        padding: 1.5rem;
        overflow-x: auto;

        code {
          color: #e0e0e0;
          font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
          font-size: 0.9rem;
          line-height: 1.6;
          white-space: pre;
        }
      }
    }
  }
}
</style>
