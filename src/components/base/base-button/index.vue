<template>
  <button
    :class="buttonClasses"
    :disabled="disabled"
    @click="handleClick">
    <slot></slot>
  </button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const buttonClasses = computed(() => [
  'c-base-button',
  `c-base-button--${props.type}`,
  `c-base-button--${props.size}`,
  {
    'c-base-button--disabled': props.disabled
  }
])

function handleClick(event) {
  if (!props.disabled) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
@use 'sass:map';
@import '@/styles/abstracts/_colors.scss';

.c-base-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;

  &--small {
    padding: 4px 12px;
    font-size: 12px;
  }

  &--medium {
    padding: 8px 16px;
    font-size: 14px;
  }

  &--large {
    padding: 12px 20px;
    font-size: 16px;
  }

  &--default {
    background-color: $color-text-white;
    border-color: $color-border-base;
    color: $color-text-primary;

    &:hover {
      border-color: $color-brand-hover;
      color: $color-functional-primary;
    }
  }

  &--primary {
    background-color: $color-brand-primary;
    border-color: $color-brand-primary;
    color: $color-text-white;

    &:hover {
      background-color: $color-brand-hover;
      border-color: $color-brand-hover;
    }
  }

  &--success {
    background-color: $color-functional-success;
    border-color: $color-functional-success;
    color: $color-text-white;

    &:hover {
      background-color: lighten($color-functional-success, 10%);
      border-color: lighten($color-functional-success, 10%);
    }
  }

  &--warning {
    background-color: $color-functional-warning;
    border-color: $color-functional-warning;
    color: $color-text-white;

    &:hover {
      background-color: lighten($color-functional-warning, 10%);
      border-color: lighten($color-functional-warning, 10%);
    }
  }

  &--danger {
    background-color: $color-functional-danger;
    border-color: $color-functional-danger;
    color: $color-text-white;

    &:hover {
      background-color: lighten($color-functional-danger, 10%);
      border-color: lighten($color-functional-danger, 10%);
    }
  }

  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      // 禁用状态不响应hover
    }
  }
}
</style>
