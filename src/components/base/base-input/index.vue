<template>
  <div class="base-input">
    <label v-if="label" :for="inputId" class="base-input__label">
      {{ label }}
      <span v-if="required" class="base-input__required">*</span>
    </label>
    
    <input
      :id="inputId"
      :type="type"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      :class="inputClasses"
      @input="handleInput"
      @blur="handleBlur"
      @focus="handleFocus">
    
    <div v-if="errorMessage" class="base-input__error">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseInput'
}
</script>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  disabled: {
    type: <PERSON>olean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'blur', 'focus'])

const inputId = ref(`input-${Math.random().toString(36).substr(2, 9)}`)

const inputClasses = computed(() => [
  'base-input__field',
  `base-input__field--${props.size}`,
  {
    'base-input__field--disabled': props.disabled,
    'base-input__field--error': props.errorMessage
  }
])

function handleInput(event) {
  emit('update:modelValue', event.target.value)
}

function handleBlur(event) {
  emit('blur', event)
}

function handleFocus(event) {
  emit('focus', event)
}
</script>

<style lang="scss" scoped>
@import '@/styles/abstracts/_colors.scss';

.base-input {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  &__label {
    font-size: 14px;
    font-weight: 500;
    color: $color-text-primary;
  }
  
  &__required {
    color: $color-functional-danger;
  }
  
  &__field {
    border: 1px solid $color-border-base;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 14px;
    
    &:focus {
      outline: none;
      border-color: $color-brand-hover;
      box-shadow: 0 0 0 2px rgba($color-brand-primary, 0.2);
    }
    
    &--small {
      padding: 4px 8px;
      font-size: 12px;
    }
    
    &--medium {
      padding: 8px 12px;
      font-size: 14px;
    }
    
    &--large {
      padding: 12px 16px;
      font-size: 16px;
    }
    
    &--disabled {
      background-color: $color-fill-disabled;
      color: $color-text-placeholder;
      cursor: not-allowed;
    }
    
    &--error {
      border-color: $color-functional-danger;
      
      &:focus {
        border-color: $color-functional-danger;
        box-shadow: 0 0 0 2px rgba($color-functional-danger, 0.2);
      }
    }
  }
  
  &__error {
    font-size: 12px;
    color: $color-functional-danger;
  }
}
</style>
