<template>
  <div class="advanced-filter">
    <div class="advanced-filter__header">
      <div class="advanced-filter__actions">
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><RefreshRight /></el-icon>
          重置
        </el-button>
        <el-button
          v-if="config.collapsible"
          text
          @click="handleToggle">
          <el-icon>
            <ArrowUp v-if="!collapsedState" />
            <ArrowDown v-if="collapsedState" />
          </el-icon>
          {{ collapsedState ? '展开' : '收起' }}
        </el-button>
      </div>
    </div>

    <div v-if="!collapsedState" class="advanced-filter__form">
      <el-form
        ref="formRef"
        :model="formData"
        :label-width="labelWidth"
        :inline="true"
        class="flex-wrap">
        <div class="flex flex-wrap w-full">
          <div
            v-for="field in visibleFields"
            :key="field.prop"
            class="form-item-wrapper">
            <el-form-item
              :label="field.label"
              :prop="field.prop"
              :rules="field.rules">
              <!-- 输入框 -->
              <el-input
                v-if="field.type === 'input'"
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                :disabled="getFieldDisabled(field)"
                clearable />

              <!-- 数字输入框 -->
              <el-input-number
                v-else-if="field.type === 'number'"
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                :disabled="getFieldDisabled(field)"
                style="width: 100%" />

              <!-- 选择器 -->
              <el-select
                v-else-if="field.type === 'select'"
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                :disabled="getFieldDisabled(field)"
                clearable
                style="width: 100%">
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value" />
              </el-select>

              <!-- 日期选择器 -->
              <el-date-picker
                v-else-if="field.type === 'date'"
                v-model="formData[field.prop]"
                type="date"
                :placeholder="field.placeholder"
                :disabled="getFieldDisabled(field)"
                style="width: 100%" />

              <!-- 日期范围选择器 -->
              <el-date-picker
                v-else-if="field.type === 'daterange'"
                v-model="formData[field.prop]"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled="getFieldDisabled(field)"
                style="width: 100%" />

              <!-- 复选框 -->
              <el-checkbox
                v-else-if="field.type === 'checkbox'"
                v-model="formData[field.prop]"
                :disabled="getFieldDisabled(field)">
                {{ field.placeholder }}
              </el-checkbox>

              <!-- 单选框组 -->
              <el-radio-group
                v-else-if="field.type === 'radio'"
                v-model="formData[field.prop]"
                :disabled="getFieldDisabled(field)">
                <el-radio
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.value">
                  {{ option.label }}
                </el-radio>
              </el-radio-group>

              <!-- 自定义组件 -->
              <component
                :is="field.component"
                v-else-if="field.type === 'custom' && field.component"
                v-model="formData[field.prop]"
                v-bind="field.componentProps"
                :disabled="getFieldDisabled(field)" />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElCheckbox,
  ElRadioGroup,
  ElRadio,
  ElButton,
  ElIcon
} from 'element-plus'
import { Search, RefreshRight, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    default: () => ({})
  },
  config: {
    type: Object,
    required: true
  },
  collapsed: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'reset', 'toggle'])

const formRef = ref()
const formData = ref({})
const internalCollapsed = ref(props.collapsed)

// 计算属性
const labelWidth = computed(() => {
  return props.config.layout === 'inline' ? 'auto' : '100px'
})

const visibleFields = computed(() => {
  return props.config.fields.filter(field => {
    if (typeof field.visible === 'function') {
      return field.visible(formData.value)
    }
    return field.visible !== false
  })
})

const collapsedState = computed({
  get: () => internalCollapsed.value,
  set: (value) => {
    internalCollapsed.value = value
    emit('toggle', value)
  }
})

// 获取字段跨度 - 现在使用CSS控制宽度，此函数保留但不再使用span属性
const getFieldSpan = (field) => {
  return 24
}

// 获取字段是否禁用
const getFieldDisabled = (field) => {
  if (typeof field.disabled === 'function') {
    return field.disabled(formData.value)
  }
  return field.disabled || false
}

// 事件处理
const handleSearch = async () => {
  try {
    const valid = formRef.value ? await formRef.value.validate() : false
    if (valid) {
      emit('update:modelValue', { ...formData.value })
      emit('search', { ...formData.value })
    }
  } catch (error) {
    console.warn('Form validation failed:', error)
  }
}

const handleReset = () => {
  if (formRef.value) formRef.value.resetFields()

  // 重置为默认值
  const resetData = {}
  props.config.fields.forEach(field => {
    if (field.defaultValue !== undefined) {
      resetData[field.prop] = field.defaultValue
    }
  })

  formData.value = resetData
  emit('update:modelValue', resetData)
  emit('reset')
}

const handleToggle = () => {
  // Use nextTick to avoid ResizeObserver errors
  nextTick(() => {
    collapsedState.value = !collapsedState.value
  })
}

// 处理字段联动
const handleFieldDependency = (field) => {
  if (!field.dependencies || !field.dependencyLogic) return

  const dependencies = field.dependencies.map(dep => formData.value[dep])
  const changes = field.dependencyLogic(formData.value, dependencies)

  if (changes) {
    // 应用联动变化
    Object.assign(field, changes)
  }
}

// 初始化表单数据
const initFormData = () => {
  const data = {}

  props.config.fields.forEach(field => {
    if (field.defaultValue !== undefined) {
      data[field.prop] = field.defaultValue
    }
  })

  // 合并传入的值
  Object.assign(data, props.modelValue)

  formData.value = data
}

// 监听表单数据变化，处理字段联动
watch(formData, (newData) => {
  props.config.fields.forEach(field => {
    if (field.dependencies?.length) {
      handleFieldDependency(field)
    }
  })
}, { deep: true })

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  formData.value = { ...newValue }
}, { deep: true })

// 监听配置变化
watch(() => props.config, () => {
  initFormData()
}, { deep: true })

// 生命周期
onMounted(() => {
  initFormData()
})
</script>

<style scoped>
.advanced-filter {
  border: 1px solid #e5e7eb; /* border-gray-200 */
  border-radius: 0.5rem;      /* rounded-lg */
  overflow: hidden;
}

.advanced-filter__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;              /* p-4 */
  background-color: #f9fafb;  /* bg-gray-50 */
  border-bottom: 1px solid #e5e7eb; /* border-b border-gray-200 */
}

.advanced-filter__title {
  display: flex;
  align-items: center;
  gap: 0.5rem;                /* gap-2 */
  font-weight: 500;           /* font-medium */
  color: #374151;             /* text-gray-700 */
}

.advanced-filter__actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;                /* gap-2 */
}

.advanced-filter__form {
  padding: 1rem;              /* p-4 */
}

.form-item-wrapper {
  flex-grow: 0;
  flex-shrink: 0;
  min-width: 240px;
  max-width: 100%;
  margin-right: 16px;
}
</style>
