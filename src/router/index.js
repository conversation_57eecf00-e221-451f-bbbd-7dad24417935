import { createRouter } from 'vue-router'
import * as VueRouter from 'vue-router'
import { useAuth } from '@/context/auth'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import AuthLayout from '@/layouts/AuthLayout.vue'

/**
 * 路由配置
 * 根据README建议：当路由配置不是非常庞大时（例如不超过50条），
 * 应该在index.js中集中管理，以降低查找和理解成本
 */
const routes = [
  // 默认布局 - 包含导航栏和页脚的页面
  {
    path: '/',
    component: DefaultLayout,
    children: [
      // 首页路由
      {
        path: '',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页'
        }
      },
      // 页面路由
      {
        path: 'design-system',
        name: 'DesignSystem',
        component: () => import('@/views/design-system/index.vue'),
        meta: {
          title: 'Design System',
          icon: 'Brush'
        }
      },
      {
        path: 'components/data-grid',
        name: 'DataGrid',
        component: () => import('@/views/components/data-grid/index.vue'),
        meta: {
          title: 'Data Grid',
          icon: 'Grid'
        }
      },
      {
        path: 'about',
        name: 'About',
        component: () => import('@/views/about/index.vue'),
        meta: {
          title: 'About Us'
        }
      },
      {
        path: 'privacy',
        name: 'Privacy',
        component: () => import('@/views/privacy/index.vue'),
        meta: {
          title: 'Privacy Policy'
        }
      },
      {
        path: 'terms',
        name: 'Terms',
        component: () => import('@/views/terms/index.vue'),
        meta: {
          title: 'Terms of Service'
        }
      },
    ]
  },

  // 内容布局 - 使用ContentLayout的页面
  // 数据看板路由
  {
    path: '/dashboard',
    component: DefaultLayout,
    redirect: '/dashboard/index',
    meta: {
      title: '数据看板',
      icon: 'DataAnalysis'
    },
    children: [
      {
        path: 'index',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '数据看板',
          icon: 'DataAnalysis',
          keepAlive: true
        }
      }
    ]
  },

  // 组件演示路由
  {
    path: '/components',
    name: 'Components',
    component: DefaultLayout,
    meta: {
      title: '组件演示',
      icon: 'Grid'
    },
    children: [
      {
        path: 'theme-test',
        name: 'ThemeTest',
        component: () => import('@/views/theme-test/index.vue'),
        meta: {
          title: 'Element Plus 主题测试',
          icon: 'Brush'
        }
      }
    ]
  },

  // 认证相关路由 - 使用AuthLayout
  {
    path: '/login',
    component: AuthLayout,
    children: [
      {
        path: '',
        name: 'login',
        component: () => import('@/views/auth/login.vue'),
        meta: {
          title: '登录'
        }
      }
    ]
  },
  // 错误处理路由
  {
    path: '/unauthorized',
    name: 'unauthorized',
    component: () => import('@/views/auth/Unauthorized.vue'),
    meta: {
      title: 'Unauthorized Access'
    }
  },
  // 捕获所有未匹配路由
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/auth/NotFound.vue'),
    meta: {
      title: 'Page Not Found'
    }
  }
]

const router = createRouter({
  history: VueRouter.createWebHashHistory(),
  routes,
})

router.beforeEach((to, from, next) => {
  try {
    const { isAuthenticated, hasPermission } = useAuth()

    // 权限验证
    if (to.meta.requiresAuth && !isAuthenticated.value) {
      next({ name: 'login', query: { redirect: to.fullPath } })
    } else if (to.meta.permissions && !hasPermission(to.meta.permissions)) {
      next({ name: 'unauthorized' })
    } else {
      next()
    }
  } catch (error) {
    // 如果身份验证上下文未提供（例如应用初始化时），则使用默认行为
    console.warn('Auth context not available, using default authentication behavior')
    if (to.meta.requiresAuth && !localStorage.getItem('token')) {
      next({ name: 'login', query: { redirect: to.fullPath } })
    } else {
      next()
    }
  }
})

// 全局后置钩子
router.afterEach((to) => {
  // 更新页面标题
  document.title = to.meta.title ? `${to.meta.title} - Galaxy Vue Demi` : 'Galaxy Vue Demi'
})

export default router
