/**
 * Context 状态管理统一导出
 * 根据 README 规范：推荐使用 context 进行状态管理
 */

// 认证相关
export { createAuthProvider, useAuth } from './auth'

// 用户相关
export { createUserProvider, useUser } from './user'

// 应用设置相关
export { createAppProvider, useApp } from './app'

// 创建所有 Provider 的便捷函数
export function createAllProviders() {
  const authProvider = createAuthProvider()
  const userProvider = createUserProvider()
  const appProvider = createAppProvider()
  
  return {
    auth: authProvider,
    user: userProvider,
    app: appProvider
  }
}
