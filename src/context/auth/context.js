import { ref, provide, inject, readonly } from 'vue'

// 创建认证状态和方法
const AuthSymbol = Symbol('auth')

export function createAuthProvider() {
  // 认证状态
  const isAuthenticated = ref(!!localStorage.getItem('token'))
  const userPermissions = ref([])
  const userProfile = ref(null)

  // 登录方法
  const login = (token, userData) => {
    localStorage.setItem('token', token)
    isAuthenticated.value = true
    userProfile.value = userData
  }

  // 登出方法
  const logout = () => {
    localStorage.removeItem('token')
    isAuthenticated.value = false
    userProfile.value = null
    userPermissions.value = []
  }

  // 检查权限
  const hasPermission = (requiredPermissions) => {
    if (!requiredPermissions || requiredPermissions.length === 0) return true
    if (!isAuthenticated.value) return false
    
    // 如果没有指定权限，则默认返回true (简化示例)
    if (!userPermissions.value || userPermissions.value.length === 0) return true
    
    // 检查是否有所需权限
    if (Array.isArray(requiredPermissions)) {
      return requiredPermissions.some(permission => 
        userPermissions.value.includes(permission)
      )
    }
    
    return userPermissions.value.includes(requiredPermissions)
  }

  // 提供给组件使用的API
  provide(AuthSymbol, {
    isAuthenticated: readonly(isAuthenticated),
    userProfile: readonly(userProfile),
    login,
    logout,
    hasPermission
  })
}

// 在组件中使用
export function useAuth() {
  const auth = inject(AuthSymbol)
  if (!auth) {
    throw new Error('useAuth() must be used within an AuthProvider')
  }
  return auth
}
