import { ref, provide, inject, readonly, watch } from 'vue'

// 创建应用设置上下文
const AppSymbol = Symbol('app')

export function createAppProvider() {
  // 应用主题设置
  const theme = ref(localStorage.getItem('app-theme') || 'light')
  const primaryColor = ref(localStorage.getItem('app-primary-color') || '#409eff')
  
  // 布局设置
  const sidebarCollapsed = ref(JSON.parse(localStorage.getItem('sidebar-collapsed') || 'false'))
  const layoutMode = ref(localStorage.getItem('layout-mode') || 'default') // default, compact, comfortable
  
  // 语言设置
  const locale = ref(localStorage.getItem('app-locale') || 'zh-CN')
  
  // 应用状态
  const loading = ref(false)
  const online = ref(navigator.onLine)
  
  // 面包屑导航
  const breadcrumbs = ref([])
  
  // 页面标题
  const pageTitle = ref('')
  
  // 监听主题变化并保存到 localStorage
  watch(theme, (newTheme) => {
    localStorage.setItem('app-theme', newTheme)
    // 更新 CSS 变量或类名
    document.documentElement.setAttribute('data-theme', newTheme)
  }, { immediate: true })
  
  // 监听主色调变化
  watch(primaryColor, (newColor) => {
    localStorage.setItem('app-primary-color', newColor)
    // 更新 CSS 变量
    document.documentElement.style.setProperty('--el-color-primary', newColor)
  }, { immediate: true })
  
  // 监听侧边栏状态
  watch(sidebarCollapsed, (collapsed) => {
    localStorage.setItem('sidebar-collapsed', JSON.stringify(collapsed))
  })
  
  // 监听布局模式
  watch(layoutMode, (mode) => {
    localStorage.setItem('layout-mode', mode)
    document.documentElement.setAttribute('data-layout-mode', mode)
  }, { immediate: true })
  
  // 监听语言变化
  watch(locale, (newLocale) => {
    localStorage.setItem('app-locale', newLocale)
    // 这里可以集成 i18n
  })
  
  // 监听网络状态
  const updateOnlineStatus = () => {
    online.value = navigator.onLine
  }
  
  window.addEventListener('online', updateOnlineStatus)
  window.addEventListener('offline', updateOnlineStatus)
  
  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
  }
  
  // 设置主题
  const setTheme = (newTheme) => {
    if (['light', 'dark', 'auto'].includes(newTheme)) {
      theme.value = newTheme
    }
  }
  
  // 设置主色调
  const setPrimaryColor = (color) => {
    primaryColor.value = color
  }
  
  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
  
  // 设置侧边栏状态
  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
  }
  
  // 设置布局模式
  const setLayoutMode = (mode) => {
    if (['default', 'compact', 'comfortable'].includes(mode)) {
      layoutMode.value = mode
    }
  }
  
  // 设置语言
  const setLocale = (newLocale) => {
    locale.value = newLocale
  }
  
  // 设置加载状态
  const setLoading = (isLoading) => {
    loading.value = isLoading
  }
  
  // 设置面包屑
  const setBreadcrumbs = (crumbs) => {
    breadcrumbs.value = crumbs
  }
  
  // 添加面包屑项
  const addBreadcrumb = (crumb) => {
    breadcrumbs.value.push(crumb)
  }
  
  // 设置页面标题
  const setPageTitle = (title) => {
    pageTitle.value = title
    document.title = title ? `${title} - Galaxy Vue Demi` : 'Galaxy Vue Demi'
  }
  
  // 重置所有设置
  const resetSettings = () => {
    setTheme('light')
    setPrimaryColor('#409eff')
    setSidebarCollapsed(false)
    setLayoutMode('default')
    setLocale('zh-CN')
  }
  
  // 导出设置
  const exportSettings = () => {
    return {
      theme: theme.value,
      primaryColor: primaryColor.value,
      sidebarCollapsed: sidebarCollapsed.value,
      layoutMode: layoutMode.value,
      locale: locale.value
    }
  }
  
  // 导入设置
  const importSettings = (settings) => {
    if (settings.theme) setTheme(settings.theme)
    if (settings.primaryColor) setPrimaryColor(settings.primaryColor)
    if (typeof settings.sidebarCollapsed === 'boolean') setSidebarCollapsed(settings.sidebarCollapsed)
    if (settings.layoutMode) setLayoutMode(settings.layoutMode)
    if (settings.locale) setLocale(settings.locale)
  }
  
  // 提供给组件使用的 API
  provide(AppSymbol, {
    // 只读状态
    theme: readonly(theme),
    primaryColor: readonly(primaryColor),
    sidebarCollapsed: readonly(sidebarCollapsed),
    layoutMode: readonly(layoutMode),
    locale: readonly(locale),
    loading: readonly(loading),
    online: readonly(online),
    breadcrumbs: readonly(breadcrumbs),
    pageTitle: readonly(pageTitle),
    
    // 方法
    toggleTheme,
    setTheme,
    setPrimaryColor,
    toggleSidebar,
    setSidebarCollapsed,
    setLayoutMode,
    setLocale,
    setLoading,
    setBreadcrumbs,
    addBreadcrumb,
    setPageTitle,
    resetSettings,
    exportSettings,
    importSettings
  })
  
  // 清理函数
  const cleanup = () => {
    window.removeEventListener('online', updateOnlineStatus)
    window.removeEventListener('offline', updateOnlineStatus)
  }
  
  return {
    cleanup
  }
}

// 在组件中使用
export function useApp() {
  const app = inject(AppSymbol)
  if (!app) {
    throw new Error('useApp() must be used within an AppProvider')
  }
  return app
}
