import { ref, provide, inject, readonly, computed } from 'vue'
import { getSystemCode, getToken, setToken, removeToken } from '@/api/utils/auth'

// 创建用户状态管理上下文
const UserSymbol = Symbol('user')

export function createUserProvider() {
  // 用户基本信息
  const userProfile = ref(null)
  const systemCode = ref(getSystemCode() || '')
  const token = ref(getToken() || '')
  const isLoggedIn = computed(() => !!token.value && !!userProfile.value)
  
  // 用户权限
  const permissions = ref([])
  const roles = ref([])
  
  // 用户偏好设置
  const preferences = ref({
    theme: 'light',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: '24h'
  })
  
  // 登录方法
  const login = async (credentials) => {
    try {
      // 这里应该调用实际的登录 API
      // const response = await loginAPI(credentials)
      
      // 模拟登录响应
      const mockResponse = {
        token: `mock_token_${Date.now()}`,
        user: {
          id: 1,
          username: credentials.username,
          email: credentials.email || `${credentials.username}@example.com`,
          name: credentials.name || '测试用户',
          avatar: '',
          roles: ['user'],
          permissions: ['read', 'write']
        }
      }
      
      // 保存 token
      setToken(mockResponse.token)
      token.value = mockResponse.token
      
      // 保存用户信息
      userProfile.value = mockResponse.user
      roles.value = mockResponse.user.roles
      permissions.value = mockResponse.user.permissions
      
      return { success: true, data: mockResponse }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }
  
  // 登出方法
  const logout = () => {
    removeToken()
    token.value = ''
    userProfile.value = null
    roles.value = []
    permissions.value = []
    
    // 重置偏好设置为默认值
    preferences.value = {
      theme: 'light',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      timeFormat: '24h'
    }
  }
  
  // 更新用户信息
  const updateProfile = (newProfile) => {
    if (userProfile.value) {
      userProfile.value = { ...userProfile.value, ...newProfile }
    }
  }
  
  // 更新用户偏好设置
  const updatePreferences = (newPreferences) => {
    preferences.value = { ...preferences.value, ...newPreferences }
    // 这里可以调用 API 保存到服务器
  }
  
  // 检查权限
  const hasPermission = (permission) => {
    if (!isLoggedIn.value) return false
    return permissions.value.includes(permission)
  }
  
  // 检查角色
  const hasRole = (role) => {
    if (!isLoggedIn.value) return false
    return roles.value.includes(role)
  }
  
  // 检查多个权限（需要全部拥有）
  const hasAllPermissions = (permissionList) => {
    if (!isLoggedIn.value) return false
    return permissionList.every(permission => permissions.value.includes(permission))
  }
  
  // 检查多个权限（拥有其中任意一个即可）
  const hasAnyPermission = (permissionList) => {
    if (!isLoggedIn.value) return false
    return permissionList.some(permission => permissions.value.includes(permission))
  }
  
  // 获取用户显示名称
  const displayName = computed(() => {
    if (!userProfile.value) return ''
    return userProfile.value.name || userProfile.value.username || userProfile.value.email
  })
  
  // 获取用户头像
  const avatarUrl = computed(() => {
    if (!userProfile.value?.avatar) {
      // 返回默认头像或生成头像
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName.value)}&background=409eff&color=fff`
    }
    return userProfile.value.avatar
  })
  
  // 提供给组件使用的 API
  provide(UserSymbol, {
    // 只读状态
    userProfile: readonly(userProfile),
    systemCode: readonly(systemCode),
    token: readonly(token),
    isLoggedIn: readonly(isLoggedIn),
    permissions: readonly(permissions),
    roles: readonly(roles),
    preferences: readonly(preferences),
    displayName: readonly(displayName),
    avatarUrl: readonly(avatarUrl),
    
    // 方法
    login,
    logout,
    updateProfile,
    updatePreferences,
    hasPermission,
    hasRole,
    hasAllPermissions,
    hasAnyPermission
  })
  
  // 初始化时检查是否已登录
  const initializeUser = async () => {
    const savedToken = getToken()
    if (savedToken) {
      token.value = savedToken
      // 这里应该调用 API 获取用户信息
      // 暂时使用模拟数据
      userProfile.value = {
        id: 1,
        username: 'demo_user',
        email: '<EMAIL>',
        name: '演示用户',
        avatar: '',
        roles: ['user'],
        permissions: ['read', 'write']
      }
      roles.value = userProfile.value.roles
      permissions.value = userProfile.value.permissions
    }
  }
  
  // 返回初始化方法供外部调用
  return {
    initializeUser
  }
}

// 在组件中使用
export function useUser() {
  const user = inject(UserSymbol)
  if (!user) {
    throw new Error('useUser() must be used within a UserProvider')
  }
  return user
}
