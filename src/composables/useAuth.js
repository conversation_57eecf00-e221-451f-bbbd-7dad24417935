import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

// 模拟用户状态
const currentUser = ref(null)
const isAuthenticated = ref(false)
const token = ref(localStorage.getItem('auth_token') || null)

export function useAuth() {
  const router = useRouter()
  
  // 模拟登录API调用
  const login = async ({ email, password, rememberMe }) => {
    try {
      // 在实际应用中，这里应该是一个API调用
      // 这里简单模拟一个成功的登录
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // 模拟验证
      if (email === '<EMAIL>' && password === 'password') {
        const userData = {
          id: 1,
          email,
          name: '测试用户',
          role: 'user'
        }
        
        // 保存用户信息
        currentUser.value = userData
        isAuthenticated.value = true
        
        // 生成模拟token
        const mockToken = `mock_token_${Date.now()}`
        token.value = mockToken
        
        // 如果选择记住我，则保存token到localStorage
        if (rememberMe) {
          localStorage.setItem('auth_token', mockToken)
        } else {
          // 会话结束后清除
          sessionStorage.setItem('auth_token', mockToken)
        }
        
        // 登录成功后重定向到首页或之前尝试访问的页面
        const redirectPath = router.currentRoute.value.query.redirect || '/'
        router.push(redirectPath)
        
        return true
      } else {
        throw new Error('邮箱或密码不正确')
      }
    } catch (error) {
      ElMessage.error(error.message || '登录失败')
      return false
    }
  }
  
  // 注册
  const register = async (userData) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      ElMessage.success('注册成功，请登录')
      router.push('/auth/login')
      return true
    } catch (error) {
      ElMessage.error(error.message || '注册失败')
      return false
    }
  }
  
  // 登出
  const logout = () => {
    currentUser.value = null
    isAuthenticated.value = false
    token.value = null
    
    // 清除存储的token
    localStorage.removeItem('auth_token')
    sessionStorage.removeItem('auth_token')
    
    // 重定向到登录页
    router.push('/auth/login')
  }
  
  // 检查是否已认证
  const checkAuth = async () => {
    // 如果已经有用户信息，则已认证
    if (currentUser.value) {
      return true
    }
    
    // 检查是否有token
    const savedToken = token.value || 
                      localStorage.getItem('auth_token') || 
                      sessionStorage.getItem('auth_token')
    
    if (savedToken) {
      try {
        // 模拟验证token的API调用
        await new Promise(resolve => setTimeout(resolve, 300))
        
        // 模拟获取用户信息
        const userData = {
          id: 1,
          email: '<EMAIL>',
          name: '测试用户',
          role: 'user'
        }
        
        // 更新状态
        currentUser.value = userData
        isAuthenticated.value = true
        token.value = savedToken
        
        return true
      } catch (error) {
        // token无效，清除
        logout()
        return false
      }
    }
    
    return false
  }
  
  return {
    currentUser,
    isAuthenticated,
    token,
    login,
    register,
    logout,
    checkAuth
  }
}
