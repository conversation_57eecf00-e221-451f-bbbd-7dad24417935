import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import store from '@/store'

import defaultSettings from '@/settings'
import { getToken } from '@/utils/auth'

const { TIMEOUT, SYSTEM_CODE } = defaultSettings

const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // api的base_url
  timeout: TIMEOUT
})

service.interceptors.request.use(config => {
  const token = getToken()
  if (token) {
    // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
    config.headers['Authorization'] = 'Bearer ' + token
  }

  if (config.method === 'get') {
    if (!config.params) {
      config.params = {}
    }
  } else {
    if (!config.data) {
      config.data = {}
    }
  }

  if (config.method === 'get') {
    config.params['relType'] = '1'
  } else {
    config.data['relType'] = '1'
  }

  return config
}, error => {
  console.log(error)
  Promise.reject(error).then(r => {})
})

service.interceptors.response.use(
  response => {
    if (response.status && response.status === 401) {
      MessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/LogOut').then(() => {
          location.reload()
        })
      })
      return Promise.reject('error')
    }
    return response.data
  },
  error => {
    console.log('err:', JSON.stringify(error))// for debug
    if (error.response && error.response.status === 403) {
      MessageBox.alert('会话超时或权限不足，请重新登录', '登出', {
        confirmButtonText: '重新登录',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/LogOut').then(() => {
          location.reload()
        })
      })
    } else {
      Message({
        message: error.message,
        type: 'error',
        duration: 5 * 1000
      })
    }
    return Promise.reject(error)
  })

export default service

