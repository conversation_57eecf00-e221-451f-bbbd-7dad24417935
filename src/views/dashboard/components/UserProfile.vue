<template>
  <div class="c-user-profile">
    <el-card class="c-user-profile__card">
      <template #header>
        <div class="c-user-profile__header">
          <h3>用户信息</h3>
          <el-button type="primary" size="small" @click="showEditDialog = true">
            编辑
          </el-button>
        </div>
      </template>
      
      <div class="c-user-profile__content">
        <div class="c-user-profile__avatar">
          <el-avatar :size="80" :src="user.avatarUrl" />
        </div>
        
        <div class="c-user-profile__info">
          <h4 class="c-user-profile__name">{{ user.displayName }}</h4>
          <p class="c-user-profile__email">{{ user.userProfile?.email }}</p>
          
          <div class="c-user-profile__meta">
            <el-tag v-for="role in user.roles" :key="role" size="small" class="c-user-profile__role">
              {{ role }}
            </el-tag>
          </div>
          
          <div class="c-user-profile__status">
            <el-badge :value="user.permissions.length" class="c-user-profile__permissions">
              <el-button size="small">权限</el-button>
            </el-badge>
            
            <el-tag :type="user.isLoggedIn ? 'success' : 'danger'" size="small">
              {{ user.isLoggedIn ? '已登录' : '未登录' }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 编辑对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑用户信息" width="500px">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="姓名">
          <el-input v-model="editForm.name" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="editForm.email" />
        </el-form-item>
        <el-form-item label="头像">
          <el-input v-model="editForm.avatar" placeholder="头像URL" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useUser } from '@/context/user'

// 使用用户 context
const user = useUser()

// 编辑对话框状态
const showEditDialog = ref(false)
const editForm = reactive({
  name: '',
  email: '',
  avatar: ''
})

// 监听用户信息变化，更新编辑表单
watch(() => user.userProfile, (profile) => {
  if (profile) {
    editForm.name = profile.name || ''
    editForm.email = profile.email || ''
    editForm.avatar = profile.avatar || ''
  }
}, { immediate: true })

// 保存用户信息
const handleSave = () => {
  try {
    user.updateProfile({
      name: editForm.name,
      email: editForm.email,
      avatar: editForm.avatar
    })
    
    ElMessage.success('用户信息更新成功')
    showEditDialog.value = false
  } catch (error) {
    ElMessage.error('更新失败：' + error.message)
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/colors' as *;

.c-user-profile {
  &__card {
    max-width: 400px;
  }
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      color: $text-color-primary;
    }
  }
  
  &__content {
    display: flex;
    gap: 16px;
    align-items: flex-start;
  }
  
  &__avatar {
    flex-shrink: 0;
  }
  
  &__info {
    flex: 1;
  }
  
  &__name {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: $text-color-primary;
  }
  
  &__email {
    margin: 0 0 12px 0;
    color: $text-color-regular;
    font-size: 14px;
  }
  
  &__meta {
    margin-bottom: 12px;
  }
  
  &__role {
    margin-right: 8px;
  }
  
  &__status {
    display: flex;
    gap: 8px;
    align-items: center;
  }
  
  &__permissions {
    margin-right: 8px;
  }
}
</style>
