<template>
  <div class="privacy-container">
    <h1>Privacy Policy</h1>
    <div class="content">
      <p class="last-updated">Last Updated: June 30, 2025</p>
      
      <section>
        <h2>1. Introduction</h2>
        <p>Welcome to Galaxy Vue Demi's Privacy Policy. This document explains how we collect, use, and protect your personal information when you use our services.</p>
      </section>
      
      <section>
        <h2>2. Information We Collect</h2>
        <p>We may collect the following types of information:</p>
        <ul>
          <li><strong>Personal Information:</strong> Name, email address, and contact details when you register or contact us.</li>
          <li><strong>Usage Data:</strong> Information about how you interact with our services, including access times, pages viewed, and features used.</li>
          <li><strong>Technical Data:</strong> IP address, browser type, device information, and operating system.</li>
        </ul>
      </section>
      
      <section>
        <h2>3. How We Use Your Information</h2>
        <p>We use the collected information for various purposes, including:</p>
        <ul>
          <li>Providing and maintaining our services</li>
          <li>Improving and personalizing user experience</li>
          <li>Communicating with you about updates or changes</li>
          <li>Analyzing usage patterns to enhance our services</li>
        </ul>
      </section>
      
      <section>
        <h2>4. Data Security</h2>
        <p>We implement appropriate security measures to protect your personal information from unauthorized access, alteration, disclosure, or destruction.</p>
      </section>
      
      <section>
        <h2>5. Contact Us</h2>
        <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.</p>
      </section>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PrivacyView'
}
</script>

<style scoped>
.privacy-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
}

h1 {
  color: var(--el-color-primary);
  margin-bottom: 20px;
}

.last-updated {
  font-style: italic;
  color: #909399;
  margin-bottom: 30px;
}

section {
  margin-bottom: 30px;
}

h2 {
  margin-bottom: 15px;
  color: var(--el-color-primary-dark-2);
}

.content {
  line-height: 1.6;
}

p {
  margin-bottom: 15px;
}

ul {
  margin-left: 20px;
  margin-bottom: 20px;
}

li {
  margin-bottom: 8px;
}

strong {
  font-weight: 600;
}
</style>
