<template>
  <div class="c-welcome-item">
    <div class="c-welcome-item__icon">
      <slot name="icon"></slot>
    </div>
    <div class="c-welcome-item__content">
      <h3 class="c-welcome-item__heading">
        <slot name="heading"></slot>
      </h3>
      <div class="c-welcome-item__description">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<style scoped>
.c-welcome-item {
  margin-top: 2rem;
  display: flex;
  position: relative;

  &__icon {
    display: flex;
    place-items: center;
    place-content: center;
    width: 32px;
    height: 32px;
    color: var(--color-text);
  }

  &__content {
    flex: 1;
    margin-left: 1rem;
  }

  &__heading {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 0.4rem;
    color: var(--color-heading);
  }

  &__description {
    line-height: 1.6;
    color: var(--color-text);
  }
}

@media (min-width: 1024px) {
  .c-welcome-item {
    margin-top: 0;
    padding: 0.4rem 0 1rem calc(var(--section-gap) / 2);

    &__icon {
      top: calc(50% - 25px);
      left: -26px;
      position: absolute;
      border: 1px solid var(--color-border);
      background: var(--color-background);
      border-radius: 8px;
      width: 50px;
      height: 50px;
    }

    &:before {
      content: ' ';
      border-left: 1px solid var(--color-border);
      position: absolute;
      left: 0;
      bottom: calc(50% + 25px);
      height: calc(50% - 25px);
    }

    &:after {
      content: ' ';
      border-left: 1px solid var(--color-border);
      position: absolute;
      left: 0;
      top: calc(50% + 25px);
      height: calc(50% - 25px);
    }

    &:first-of-type:before {
      display: none;
    }

    &:last-of-type:after {
      display: none;
    }
  }
}
</style>
