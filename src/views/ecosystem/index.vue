<template>
  <div class="c-ecosystem-page">
    <div class="l-container">
      <header class="c-ecosystem-page__header">
        <h1 class="c-ecosystem-page__title">Vue 生态系统</h1>
        <p class="c-ecosystem-page__description">
          探索 Vue.js 丰富的生态系统，了解官方工具和社区资源
        </p>
      </header>
      
      <main class="c-ecosystem-page__content">
        <TheWelcome />
      </main>
    </div>
  </div>
</template>

<script setup>
import TheWelcome from './components/TheWelcome.vue'
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/colors' as *;

.c-ecosystem-page {
  min-height: 100vh;
  padding: 40px 0;
  
  &__header {
    text-align: center;
    margin-bottom: 60px;
  }
  
  &__title {
    font-size: 48px;
    font-weight: 700;
    color: $text-color-primary;
    margin-bottom: 16px;
  }
  
  &__description {
    font-size: 18px;
    color: $text-color-regular;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
  
  &__content {
    max-width: 1200px;
    margin: 0 auto;
  }
}

.l-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}
</style>
