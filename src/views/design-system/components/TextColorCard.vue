<template>
  <div class="text-color-card">
    <div class="text-preview" :class="{ 'dark-bg': color.darkBg }">
      <p :style="{ color: color.value }" class="sample-text">{{ color.sample }}</p>
    </div>
    <div class="text-info">
      <div class="text-name-value">
        <span class="text-name">{{ color.name }}</span>
        <span class="text-value">{{ color.value }}</span>
      </div>
      <div v-if="color.scss" class="scss-value">{{ color.scss }}</div>
      <div v-if="color.description" class="text-description" :style="{ color: color.value }">{{ color.description }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  color: {
    type: Object,
    required: true,
    validator: (prop) => {
      return prop.name && prop.value && prop.description !== undefined && prop.sample
    }
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/abstracts/_colors.scss';
@import '@/styles/base/_typography.scss';

.text-color-card {
  background: white;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  border: none;

  .text-preview {
    width: 100%;
    padding: 12px;
    margin-bottom: 8px;
    background: white;
    border: 1px solid $color-border-base;

    &.dark-bg {
      background: $color-text-primary;
    }

    .sample-text {
      margin: 0;
      font-weight: 500;
      font-size: 14px;
    }
  }

  .text-info {
    .text-name-value {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
    }

    .text-name {
      font-size: 14px;
      color: $color-text-primary;
      font-weight: 500;
    }

    .text-value {
      font-size: 14px;
      color: $color-text-secondary;
    }

    .scss-value {
      font-size: 12px;
      color: $color-text-tertiary;
      margin-bottom: 4px;
    }

    .text-description {
      font-size: 12px;
      color: $color-text-secondary;
      margin-top: 4px;
    }
  }
}
</style>
