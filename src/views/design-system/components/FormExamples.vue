<template>
  <div class="form-examples">
    <el-row :gutter="24">
      <!-- 基础输入框 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>基础输入框</h4>
          <el-input v-model="input" placeholder="请输入内容" />
          <div class="component-description">
            基础输入框，用于用户输入单行文本
          </div>
        </div>
      </el-col>

      <!-- 带图标的输入框 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>带图标的输入框</h4>
          <el-input 
            v-model="inputWithIcon" 
            placeholder="请输入搜索内容"
            :prefix-icon="Search" />
          <div class="component-description">
            带有前缀图标的输入框，常用于搜索场景
          </div>
        </div>
      </el-col>

      <!-- 密码输入框 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>密码输入框</h4>
          <el-input 
            v-model="password" 
            type="password" 
            placeholder="请输入密码" 
            show-password />
          <div class="component-description">
            密码输入框，带有显示/隐藏密码的功能
          </div>
        </div>
      </el-col>

      <!-- 文本域 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>文本域</h4>
          <el-input 
            v-model="textarea" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入多行文本" />
          <div class="component-description">
            文本域输入框，用于输入多行文本内容
          </div>
        </div>
      </el-col>

      <!-- 数字输入框 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>数字输入框</h4>
          <el-input-number 
            v-model="num" 
            :min="1" 
            :max="10" />
          <div class="component-description">
            数字输入框，可设置最小值和最大值
          </div>
        </div>
      </el-col>

      <!-- 选择器 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>选择器</h4>
          <el-select v-model="select" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
          <div class="component-description">
            下拉选择器，用于在多个选项中选择一项
          </div>
        </div>
      </el-col>

      <!-- 级联选择器 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>级联选择器</h4>
          <el-cascader
            v-model="cascaderValue"
            :options="cascaderOptions"
            placeholder="请选择" />
          <div class="component-description">
            级联选择器，用于多层级数据的选择
          </div>
        </div>
      </el-col>

      <!-- 单选框组 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>单选框组</h4>
          <el-radio-group v-model="radio">
            <el-radio :label="1">选项1</el-radio>
            <el-radio :label="2">选项2</el-radio>
            <el-radio :label="3">选项3</el-radio>
          </el-radio-group>
          <div class="component-description">
            单选框组，用于在多个选项中选择一项
          </div>
        </div>
      </el-col>

      <!-- 复选框组 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>复选框组</h4>
          <el-checkbox-group v-model="checkList">
            <el-checkbox label="选项1" />
            <el-checkbox label="选项2" />
            <el-checkbox label="选项3" />
          </el-checkbox-group>
          <div class="component-description">
            复选框组，用于多选场景
          </div>
        </div>
      </el-col>

      <!-- 开关 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>开关</h4>
          <el-switch v-model="switchValue" />
          <div class="component-description">
            开关组件，表示两种相互对立的状态
          </div>
        </div>
      </el-col>

      <!-- 滑块 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>滑块</h4>
          <el-slider v-model="sliderValue" />
          <div class="component-description">
            滑块组件，用于在给定范围内选择一个值
          </div>
        </div>
      </el-col>

      <!-- 日期选择器 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>日期选择器</h4>
          <el-date-picker
            v-model="date"
            type="date"
            placeholder="选择日期" />
          <div class="component-description">
            日期选择器，用于选择日期
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'

// 基础输入框
const input = ref('')
const inputWithIcon = ref('')
const password = ref('')
const textarea = ref('')

// 数字输入框
const num = ref(1)

// 选择器
const select = ref('')
const options = [
  { value: 'option1', label: '选项1' },
  { value: 'option2', label: '选项2' },
  { value: 'option3', label: '选项3' },
  { value: 'option4', label: '选项4' },
  { value: 'option5', label: '选项5' }
]

// 级联选择器
const cascaderValue = ref([])
const cascaderOptions = [
  {
    value: 'guide',
    label: '指南',
    children: [
      {
        value: 'design',
        label: '设计原则',
        children: [
          { value: 'consistency', label: '一致性' },
          { value: 'feedback', label: '反馈' }
        ]
      },
      {
        value: 'navigation',
        label: '导航',
        children: [
          { value: 'side-nav', label: '侧边导航' },
          { value: 'top-nav', label: '顶部导航' }
        ]
      }
    ]
  }
]

// 单选框
const radio = ref(1)

// 复选框
const checkList = ref(['选项1'])

// 开关
const switchValue = ref(true)

// 滑块
const sliderValue = ref(50)

// 日期选择器
const date = ref('')
</script>

<style lang="scss" scoped>
@import '@/styles/abstracts/_colors.scss';
@import '@/styles/base/_typography.scss';

.form-examples {
  .component-container {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid $color-border-base;
    border-radius: 4px;
    background-color: #ffffff;
    
    h4 {
      @include heading-2;
      font-size: 16px;
      margin-top: 0;
      margin-bottom: 12px;
      color: $color-text-primary;
    }
    
    .component-description {
      margin-top: 12px;
      @include small-text;
      color: $color-text-secondary;
    }
  }
}
</style>
