<template>
  <div class="design-system">
    <!-- 页面头部 -->
    <div class="header-section">
      <h1 class="page-title">设计规范</h1>
    </div>

    <!-- 品牌色 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="section-header">
          <h2>品牌色</h2>
          <p>主要的品牌色彩定义，包含常规、悬浮、点击等不同状态</p>
        </div>
      </template>
      <el-row :gutter="24">
        <el-col v-for="color in brandColors" :key="color.name" :xs="24" :sm="12" :md="8" :lg="6">
          <ColorCard :color="color" />
        </el-col>
      </el-row>
    </el-card>

    <!-- 功能色 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="section-header">
          <h2>功能色</h2>
          <p>不同功能状态下使用的颜色规范</p>
        </div>
      </template>
      <div class="functional-colors">
        <el-row :gutter="24">
          <el-col
            v-for="(group) in functionalColors" :key="group.title" :xs="24" :sm="12"
            :lg="12" class="functional-group">
            <h3>{{ group.title }}</h3>
            <p class="group-description">{{ group.description }}</p>
            <el-row :gutter="16">
              <el-col :span="12">
                <ColorCard
                  :color="{
                    name: '主色',
                    value: group.mainColor,
                    description: '主要颜色',
                    scss: group.mainScss,
                  }" />
              </el-col>
              <el-col :span="12">
                <ColorCard
                  :color="{
                    name: '标签背景',
                    value: group.bgColor,
                    description: '对应的标签背景色',
                    scss: group.bgScss,
                  }" />
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 字体颜色 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="section-header">
          <h2>字体颜色</h2>
          <p>不同层级和用途的文字颜色规范</p>
        </div>
      </template>
      <div class="text-colors">
        <h3>基础文字颜色</h3>
        <el-row :gutter="16">
          <el-col v-for="color in textColors" :key="color.name" :xs="24" :sm="12" :md="8" :lg="6">
            <TextColorCard :color="color" />
          </el-col>
        </el-row>

        <el-divider />

        <h3>状态文字颜色</h3>
        <el-row :gutter="16">
          <el-col
            v-for="color in statusTextColors" :key="color.name" :xs="24" :sm="12" :md="8"
            :lg="6">
            <TextColorCard :color="color" />
          </el-col>
        </el-row>

        <el-alert
          title="注意：disable状态是normal的30%不透明度"
          type="warning"
          :closable="false"
          show-icon
          class="notice-alert" />
      </div>
    </el-card>

    <!-- 基础颜色 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="section-header">
          <h2>基础颜色</h2>
          <p>描边色和填充色规范</p>
        </div>
      </template>
      <div class="base-colors">
        <h3>描边色</h3>
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="8">
            <ColorCard
              :color="{
                name: '常规描边',
                value: '#E8EAED',
                description: '下拉选择/表格描边',
                scss: '$color-border-base',
              }" />
          </el-col>
        </el-row>

        <el-divider />

        <h3>填充色</h3>
        <el-row :gutter="16">
          <el-col
            v-for="color in fillColors" :key="color.name" :xs="24" :sm="12" :md="6"
            :lg="24/5">
            <ColorCard :color="color" />
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 字体规范 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="section-header">
          <h2>字号/行高规范</h2>
          <p>不同场景下的字体大小和行高标准</p>
        </div>
      </template>
      <div class="typography">
        <h3>字号/行高规范表</h3>
        <el-table :data="typographyData" border style="width: 100%" class="typography-table">
          <el-table-column prop="font" label="默认字体" width="120">
            <template #default="{row}">
              <span :class="`font-example-${row.type} font-bold`">{{ row.font }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="字号" width="80">
            <template #default="{row}">
              <span :class="`font-example-${row.type} font-bold`">{{ row.size }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="lineHeight" label="行高" width="80">
            <template #default="{row}">
              <span :class="`font-example-${row.type} font-bold`">{{ row.lineHeight }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="usage" label="应用">
            <template #default="{row}">
              <span :class="`font-example-${row.type} font-bold`">{{ row.usage }}</span>
            </template>
          </el-table-column>
        </el-table>

        <el-divider />

        <h3>数据表格示例</h3>
        <DataTable @view="handleView" @edit="handleEdit" @delete="handleDelete" />

        <el-divider />

        <h3>字体示例</h3>
        <el-row :gutter="16">
          <el-col
            v-for="example in typographyExamples"
            :key="example.name"
            :xs="24"
            :sm="12"
            :md="8">
            <TypographyExample :example="example" />
          </el-col>
        </el-row>

        <el-alert
          title="注意：disable状态是normal的30%不透明度"
          type="warning"
          :closable="false"
          show-icon
          class="notice-alert" />
      </div>
    </el-card>

    <!-- 表单组件 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="section-header">
          <h2>表单组件</h2>
          <p>常用的表单组件示例，包括输入框、选择器、单选框、复选框等</p>
        </div>
      </template>
      <FormExamples />
    </el-card>

    <!-- 导航组件 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="section-header">
          <h2>导航组件</h2>
          <p>常用的导航组件示例，包括标签页、面包屑、分页、步骤条等</p>
        </div>
      </template>
      <NavigationExamples />
    </el-card>

    <!-- 反馈组件 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="section-header">
          <h2>反馈组件</h2>
          <p>常用的反馈组件示例，包括提示框、通知、对话框、加载中等</p>
        </div>
      </template>
      <FeedbackExamples />
    </el-card>

    <!-- 数据展示组件 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="section-header">
          <h2>数据展示组件</h2>
          <p>常用的数据展示组件示例，包括标签、徽章、卡片、折叠面板等</p>
        </div>
      </template>
      <DataDisplayExamples />
    </el-card>

    <!-- SCSS 变量导出 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="section-header">
          <h2>SCSS 变量</h2>
          <p>可直接复制使用的 SCSS 颜色变量</p>
        </div>
      </template>
      <div class="scss-section">
        <el-button type="primary" class="copy-button" @click="copyScssVariables">
          <el-icon>
            <DocumentCopy />
          </el-icon>
          复制 SCSS 变量
        </el-button>
        <pre class="scss-code"><code>{{ scssVariables }}</code></pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy } from '@element-plus/icons-vue'
import ColorCard from './components/ColorCard.vue'
import TextColorCard from './components/TextColorCard.vue'
import TypographyExample from './components/TypographyExample.vue'
import DataTable from './components/DataTable.vue'
import FormExamples from './components/FormExamples.vue'
import NavigationExamples from './components/NavigationExamples.vue'
import FeedbackExamples from './components/FeedbackExamples.vue'
import DataDisplayExamples from './components/DataDisplayExamples.vue'

// 品牌色数据
const brandColors = ref([
  {
    name: '常规色',
    value: '#0F65DD',
    description: '主要品牌色，用于常规状态',
    scss: '$color-brand-primary'
  },
  { name: '悬浮', value: '#4A8CE8', description: '鼠标悬浮时的颜色', scss: '$color-brand-hover' },
  { name: '点击', value: '#1759B5', description: '点击激活时的颜色', scss: '$color-brand-active' },
  {
    name: '标签背景色',
    value: '#E6EDFC',
    description: '标签组件的背景色',
    scss: '$color-brand-tag-bg'
  },
  {
    name: '表头背景色',
    value: '#EDF3FD',
    description: '表格表头的背景色',
    scss: '$color-brand-table-header'
  }
])

// 功能色数据
const functionalColors = ref([
  {
    title: '通过/链接/常规操作',
    mainColor: '#0F65DD',
    bgColor: '#E6EDFC',
    description: '通过文字/链接文字/常规操作按钮文字',
    mainScss: '$color-functional-primary',
    bgScss: '$color-functional-primary-bg'
  },
  {
    title: '警示/驳回/涨停',
    mainColor: '#C20000',
    bgColor: '#FFEBEB',
    description: '警示/驳回文字/涨停',
    mainScss: '$color-functional-danger',
    bgScss: '$color-functional-danger-bg'
  },
  {
    title: '预警',
    mainColor: '#E98900',
    bgColor: '#FFEED6',
    description: '预警文字',
    mainScss: '$color-functional-warning',
    bgScss: '$color-functional-warning-bg'
  },
  {
    title: '进行中/成功/跌停',
    mainColor: '#148A0C',
    bgColor: '#EDFDEC',
    description: '进行中/成功/跌停文字',
    mainScss: '$color-functional-success',
    bgScss: '$color-functional-success-bg'
  }
])

// 文字颜色数据
const textColors = ref([
  {
    name: '主要文字',
    value: '#20212B',
    description: '一级内容/页面标题',
    sample: '这是主要文字示例',
    scss: '$color-text-primary'
  },
  {
    name: '次要文字',
    value: '#5F6978',
    description: '二级内容文字',
    sample: '这是次要文字示例',
    scss: '$color-text-secondary'
  },
  {
    name: '辅助说明文字',
    value: '#8F98A8',
    description: '辅助说明文字/图标',
    sample: '这是辅助说明文字示例',
    scss: '$color-text-tertiary'
  },
  {
    name: '预设文字',
    value: '#C5CBD6',
    description: '预设文字',
    sample: '这是预设文字示例',
    scss: '$color-text-placeholder'
  },
  {
    name: 'Button/操作文字',
    value: '#FFFFFF',
    description: '按钮和操作文字',
    sample: '这是操作文字示例',
    darkBg: true,
    scss: '$color-text-white'
  },
  {
    name: '链接文字',
    value: '#0F65DD',
    description: '通过文字/链接文字',
    sample: '这是链接文字示例',
    scss: '$color-text-link'
  }
])

const statusTextColors = ref([
  {
    name: '警示/驳回',
    value: '#C20000',
    description: '警示/驳回文字/涨停',
    sample: '警示文字示例',
    scss: '$color-text-danger'
  },
  {
    name: '预警',
    value: '#E98900',
    description: '预警文字',
    sample: '预警文字示例',
    scss: '$color-text-warning'
  },
  {
    name: '成功/进行中',
    value: '#148A0C',
    description: '进行中/成功/跌停文字',
    sample: '成功文字示例',
    scss: '$color-text-success'
  }
])

// 填充色数据
const fillColors = ref([
  {
    name: '强调色',
    value: '#5F6978',
    description: '强调/图标/特殊场景/次要文字',
    scss: '$color-fill-emphasis'
  },
  { name: '滚动条', value: '#D1D5DB', description: '滚动条/步骤条', scss: '$color-fill-scrollbar' },
  {
    name: '表头背景',
    value: '#EDF3FD',
    description: '表头背景色',
    scss: '$color-fill-table-header'
  },
  { name: '标签背景', value: '#E8EAED', description: '标签背景', scss: '$color-fill-tag' },
  {
    name: '禁用色',
    value: '#F0F0F2',
    description: '禁用(下拉选择/表单填写)',
    scss: '$color-fill-disabled'
  },
  { name: '页面背景', value: '#F3F5FB', description: '页面背景色', scss: '$color-fill-page-bg' },
  { name: '表格背景', value: '#F9FAFE', description: '表格背景色', scss: '$color-fill-table-bg' }
])

// 字体规范数据
const typographyData = ref([
  { font: '页面标题', size: '24', lineHeight: '33', usage: '个别页面', type: 'page-title', status: 'unread', time: '2024-04-21 16:02:30' },
  { font: '一级标题', size: '20', lineHeight: '28', usage: '模块标题，导航文字', type: 'h1', status: 'read', time: '2024-04-21 15:45:12' },
  { font: '二级标题', size: '16', lineHeight: '22', usage: '子模块标题', type: 'h2', status: 'unread', time: '2024-04-21 14:30:45' },
  { font: '正文', size: '14', lineHeight: '20', usage: '正文内容', type: 'body', status: 'read', time: '2024-04-21 13:15:22' },
  { font: '内容', size: '14', lineHeight: '20', usage: '表格内容，表单文字', type: 'content', status: 'unread', time: '2024-04-21 12:05:18' },
  { font: '小字', size: '12', lineHeight: '17', usage: '辅助文字，提示文字', type: 'small', status: 'read', time: '2024-04-21 11:30:05' }
])

const typographyExamples = ref([
  { name: '页面标题', size: '24px', lineHeight: '33px', text: '页面标题示例' },
  { name: '一级标题', size: '20px', lineHeight: '28px', text: '一级标题示例' },
  { name: '二级标题', size: '18px', lineHeight: '25px', text: '二级标题示例' },
  { name: '正文文字', size: '16px', lineHeight: '22px', text: '正文文字示例' },
  { name: '内容文字', size: '14px', lineHeight: '20px', text: '内容文字示例' },
  { name: '版权文字', size: '12px', lineHeight: '17px', text: '版权文字示例' }
])

// SCSS 变量字符串
const scssVariables = ref(`
// 品牌色
$color-brand-primary: #0f65dd; // 常规色
$color-brand-hover: #4a8ce8; // 悬浮
$color-brand-active: #1759b5; // 点击
$color-brand-tag-bg: #e6edfc; // 标签背景色
$color-brand-table-header: #edf3fd; // 表头背景色

// 功能色
$color-functional-primary: #0f65dd; // 通过文字/链接文字/常规操作按钮文字
$color-functional-primary-bg: #e6edfc; // 标签背景
$color-functional-danger: #c20000; // 警示/驳回文字/涨停
$color-functional-danger-bg: #ffebeb; // 标签背景
$color-functional-warning: #e98900; // 预警文字
$color-functional-warning-bg: #ffeed6; // 标签背景
$color-functional-success: #148a0c; // 进行中/成功/跌停文字
$color-functional-success-bg: #edfdec; // 标签背景

// 字体颜色
$color-text-primary: #20212b; // 主要文字(一级内容/页面标题)
$color-text-secondary: #5f6978; // 次要文字
$color-text-tertiary: #8f98a8; // 辅助说明文字/图标
$color-text-placeholder: #c5cbd6; // 预设文字
$color-text-white: #ffffff; // Button/操作文字
$color-text-link: #0f65dd; // 通过文字/链接文字/常规操作按钮文字
$color-text-danger: #c20000; // 警示/驳回文字/涨停
$color-text-warning: #e98900; // 预警文字
$color-text-success: #148a0c; // 进行中/成功/跌停文字

// 基础颜色 - 描边色
$color-border-base: #e8eaed; // 常规描边(下拉选择/表格)

// 基础颜色 - 填充色
$color-fill-emphasis: #5f6978; // 强调/图标/特殊场景/次要文字
$color-fill-scrollbar: #d1d5db; // 滚动条/步骤条
$color-fill-table-header: #edf3fd; // 表头背景色
$color-fill-tag: #e8eaed; // 标签背景
$color-fill-disabled: #f0f0f2; // 禁用(下拉选择/表单填写)
$color-fill-page-bg: #F3F5FB; // 页面背景色
$color-fill-table-bg: #f9fafe; // 表格背景色

// 语义化颜色
$color-primary: $color-functional-primary;
$color-success: $color-functional-success;
$color-warning: $color-functional-warning;
$color-danger: $color-functional-danger;
$color-info: $color-functional-primary;

// 兼容性别名
$text-color-primary: $color-text-primary;
$text-color-regular: $color-text-secondary;
$text-color-secondary: $color-text-tertiary;
$text-color-placeholder: $color-text-placeholder;
$border-color-base: $color-border-base;
$bg-color-page: $color-fill-page-bg;
`)

// 复制 SCSS 变量
const copyScssVariables = async () => {
  try {
    await navigator.clipboard.writeText(scssVariables.value)
    ElMessage.success('SCSS 变量已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const handleView = (row) => {
  ElMessage.success(`查看: ${row.name || row.font}`)
}

const handleEdit = (row) => {
  ElMessage.success(`编辑: ${row.name || row.font}`)
}

const handleDelete = (row) => {
  ElMessage.warning(`删除: ${row.name || row.font}`)
}
</script>

<style lang="scss" scoped>
// 导入顺序通常是 variables -> mixins -> base styles
@import '@/styles/abstracts/_colors.scss';
@import '@/styles/base/_typography.scss';

.design-system {
  min-height: 100vh;
  background-color: $color-fill-page-bg;
  padding: 0;
  margin: 0;

  .header-section {
    text-align: left;
    background-color: #0F65DD;
    color: white;
    padding: 16px 24px;
    margin-bottom: 32px;

    .page-title {
      @include text-style('page-title', 'bold');
      color: white;
      margin: 0;
    }
  }

  .section-card {
    margin: 0 24px 24px;
    border: 1px solid $color-border-base;
    box-shadow: none !important;

    &:last-child {
      margin-bottom: 24px;
    }

    .section-header {
      h2 {
        // 使用 'h1' mixin
        @include heading-1;
        color: $color-text-primary;
        margin-bottom: 4px;
      }

      p {
        // 使用 'content' mixin
        @include content-text;
        color: $color-text-secondary;
        margin: 0;
      }
    }
  }

  // 通用 h3 标题样式，如果各处 h3 样式统一
  h3 {
    // 使用 'h2' mixin
    @include heading-2;
    color: $color-text-primary;
    margin-top: 16px;
    margin-bottom: 12px;
  }

  .functional-colors {
    .functional-group {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        margin-bottom: 4px;
      }

      .group-description {
        // 使用 'content' mixin
        @include content-text;
        color: $color-text-secondary;
        margin-bottom: 12px;
      }
    }
  }

  .text-colors {
    h3 {
      margin-bottom: 12px;
    }

    .notice-alert {
      margin-top: 16px;
      margin-bottom: 8px;
      font-size: 12px;
    }
  }

  .base-colors {
    h3 {
      margin-bottom: 12px;
    }

    .el-divider {
      margin: 20px 0;
    }
  }

  .typography {
    span {
      color: $color-text-primary;
    }

    .typography-table {
      margin-bottom: 20px;

      :deep(.el-table__header) {
        background-color: $color-brand-table-header;
      }

      :deep(.el-table__body) {
        background-color: $color-fill-table-bg;
      }

      .font-bold {
        font-weight: bold;
      }

      .font-example-page-title {
        @include page-title;
      }

      .font-example-h1 {
        @include heading-1;
      }

      .font-example-h2 {
        @include heading-2;
      }

      .font-example-body {
        @include body-text;
      }

      .font-example-content {
        @include content-text;
      }

      .font-example-small {
        @include small-text;
      }
    }

    h3 {
      margin-bottom: 12px;
    }

    .notice-alert {
      margin-top: 16px;
      font-size: 12px;
    }

    .el-divider {
      margin: 20px 0;
    }
  }

  .scss-section {
    .copy-button {
      margin-bottom: 12px;
    }

    .scss-code {
      // 使用 'small' mixin，并指定字体为 'mono'
      @include text-style('small', 'normal', 'mono');
      background-color: #f6f8fa;
      border: 1px solid $color-border-base;
      border-radius: 4px;
      padding: 12px;
      color: $color-text-primary;
      overflow-x: auto;
      white-space: pre;
      margin: 0;
      font-size: 12px;
    }
  }
}
</style>
