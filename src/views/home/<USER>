<script setup>
import Banner from './components/Banner.vue'
import FeatureGrid from './components/FeatureGrid.vue'

// Banner 事件处理
const handleActionPrimary = () => {
  console.log('主要操作点击')
}

const handleActionSecondary = () => {
  console.log('次要操作点击')
}
</script>

<template>
  <div class="home-page">
    <div class="container">
      <Banner
        title="欢迎使用 Galaxy Vue Demi"
        description="基于 Vue 3 和 Element Plus 的现代化前端项目模板，遵循设计规范，提供完整的开发解决方案"
        @action-primary="handleActionPrimary"
        @action-secondary="handleActionSecondary" />

      <FeatureGrid />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.home-page {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }
}
</style>
