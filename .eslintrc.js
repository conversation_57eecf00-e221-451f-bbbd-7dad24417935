module.exports = {
  root: true,
  parser: 'vue-eslint-parser',
  parserOptions: {
    parser: '@babel/eslint-parser',
    sourceType: 'module',
    requireConfigFile: false,
    babelOptions: {
      presets: ['@babel/preset-env'],
    },
  },
  env: {
    browser: true,
    node: true,
    es6: true,
    jest: true,
  },
  extends: [
    'plugin:vue/vue3-recommended',
    'eslint:recommended',
  ],
  // it is base on https://github.com/vuejs/eslint-config-vue
  rules: {
    // Vue 规则
    'vue/singleline-html-element-content-newline': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    'vue/no-v-html': 'off',
    'vue/max-attributes-per-line': ['off', {
      singleline: 2,
      multiline: 1,
    }],
    'vue/multi-word-component-names': 'off', // 禁用组件名必须是多词的规则
    'vue/html-self-closing': ['error', {
      html: {
        void: 'never',
        normal: 'never',
        component: 'always'
      },
      svg: 'always',
      math: 'always'
    }],
    'vue/attributes-order': 'warn',
    'vue/attribute-hyphenation': 'warn',
    'vue/html-closing-bracket-newline': ['warn', {
      singleline: 'never',
      multiline: 'never'
    }],
    'vue/html-indent': ['warn', 2],
    
    // JavaScript 规则
    'no-unused-vars': ['warn', {
      vars: 'all',
      args: 'after-used',
      ignoreRestSiblings: true
    }],
    'block-spacing': [2, 'always'],
    'trailing-comma': 0,
    'comma-style': [2, 'last'],
  },
}
